<template>
  <div class="login-container">
    <div class="login-card">
      <div class="logo-container">
        <img src="/logo.svg" alt="KACON Logo" class="logo" @error="handleImageError">
        <h1>KACON办公管理系统</h1>
      </div>
      
      <el-form :model="loginForm" :rules="rules" ref="loginFormRef" label-position="top">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="loginForm.username" placeholder="请输入用户名" :prefix-icon="User" />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" :prefix-icon="Lock" show-password />
        </el-form-item>

        <!-- 错误信息显示 -->
        <div v-if="loginError" class="login-error">
          <el-alert
            :title="loginError"
            type="error"
            :closable="true"
            @close="loginError = ''"
            show-icon
          />
        </div>

        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleLogin" class="login-button">登录</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { ElMessage } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()
const loginFormRef = ref(null)
const loading = ref(false)
const loginError = ref('')

const loginForm = reactive({
  username: '',
  password: ''
})

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

const handleImageError = (event) => {
  console.error('Logo image failed to load:', event)
  // 可以设置一个备用的文本或者其他处理
  event.target.style.display = 'none'
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  // 清除之前的错误信息
  loginError.value = ''

  try {
    const valid = await loginFormRef.value.validate()
    if (valid) {
      loading.value = true
      
      try {
        console.log('开始登录，用户名:', loginForm.username)
        console.log('登录表单数据:', loginForm)
        await authStore.login(loginForm)

        // 登录成功
        // 仅在开发环境下显示调试信息
        if (process.env.NODE_ENV === 'development') {
          console.log('登录成功，用户ID:', authStore.user?.id)
          console.log('权限数量:', authStore.permissions?.length || 0)
        }

        // 如果是管理员，直接进入系统
        if (authStore.user?.username?.includes('admin') ||
            authStore.user?.roles?.some(r => r.name === '管理员' || r.code === 'admin')) {
          // 管理员账号
        } else {
          // 检查非管理员账号是否有权限
          if (!authStore.permissions || authStore.permissions.length === 0) {
            ElMessage.warning('当前账号未分配任何权限，请联系管理员为您分配权限')
          }
        }

        ElMessage.success('登录成功')
        router.push('/')
      } catch (error) {
        console.error('Login error:', error)

        if (error.response) {
          // 服务器返回了错误响应
          const status = error.response.status
          const message = error.response.data?.message || error.response.data?.error || ''

          switch (status) {
            case 400:
              loginError.value = '请求参数错误，请检查用户名和密码格式'
              ElMessage({
                message: '请求参数错误，请检查用户名和密码格式',
                type: 'error',
                duration: 3000,
                showClose: true
              })
              break
            case 401:
              // 删除错误提示信息
              break
            case 403:
              loginError.value = '账号已被禁用，请联系管理员'
              ElMessage({
                message: '账号已被禁用，请联系管理员',
                type: 'error',
                duration: 3000,
                showClose: true
              })
              break
            case 404:
              loginError.value = '登录服务未找到，请联系技术支持'
              ElMessage({
                message: '登录服务未找到，请联系技术支持',
                type: 'error',
                duration: 3000,
                showClose: true
              })
              break
            case 500:
              loginError.value = '服务器内部错误，请稍后重试'
              ElMessage({
                message: '服务器内部错误，请稍后重试',
                type: 'error',
                duration: 3000,
                showClose: true
              })
              break
            default:
              const errorMsg = message || `登录失败 (错误代码: ${status})`
              loginError.value = errorMsg
              ElMessage({
                message: errorMsg,
                type: 'error',
                duration: 3000,
                showClose: true
              })
          }
        } else if (error.request) {
          // 请求已发送但没有收到响应
          loginError.value = '无法连接到服务器，请检查网络连接'
          ElMessage({
            message: '无法连接到服务器，请检查网络连接或联系技术支持',
            type: 'error',
            duration: 3000,
            showClose: true
          })
        } else {
          // 请求配置出错
          loginError.value = '登录请求发送失败，请刷新页面重试'
          ElMessage({
            message: '登录请求发送失败，请刷新页面重试',
            type: 'error',
            duration: 3000,
            showClose: true
          })
        }
      } finally {
        loading.value = false
      }
    }
  } catch (validationError) {
    // 表单验证失败
    console.error('表单验证失败:', validationError)
    loginError.value = '请填写完整的用户名和密码'
  }
}
</script>

<style scoped>
.login-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, 
    #667eea 0%,
    #764ba2 25%,
    #6b8dd6 50%,
    #4facfe 75%,
    #00f2fe 100%
  );
  background-size: 400% 400%;
  animation: gradientBG 15s ease infinite;
  position: relative;
  overflow: hidden;
}

@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.login-container::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  z-index: 1;
}

.login-card {
  width: 400px;
  padding: 40px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  z-index: 2;
  transition: transform 0.3s ease;
}

.login-card:hover {
  transform: translateY(-5px);
}

.logo-container {
  text-align: center;
  margin-bottom: 30px;
}

.logo {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
}

h1 {
  font-size: 24px;
  color: #fff;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.el-form-item__label) {
  color: #fff !important;
}

:deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.1) !important;
  box-shadow: none !important;
  border: none !important;
}

:deep(.el-input__inner) {
  color: #fff !important;
}

:deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.6) !important;
}

:deep(.el-input__prefix) {
  color: rgba(255, 255, 255, 0.8) !important;
}

:deep(.el-input__prefix .el-icon) {
  font-size: 16px !important;
  transition: color 0.3s ease;
}

:deep(.el-input__wrapper:hover .el-input__prefix .el-icon) {
  color: rgba(255, 255, 255, 1) !important;
}

:deep(.el-input__wrapper.is-focus .el-input__prefix .el-icon) {
  color: #409EFF !important;
  transform: scale(1.1);
}

.login-error {
  margin-bottom: 16px;
}

:deep(.login-error .el-alert) {
  background: rgba(254, 240, 240, 0.9) !important;
  border: 1px solid rgba(245, 108, 108, 0.3) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(5px);
}

:deep(.login-error .el-alert__title) {
  color: #F56C6C !important;
  font-weight: 500;
}

:deep(.login-error .el-alert__icon) {
  color: #F56C6C !important;
}

.login-button {
  width: 100%;
  margin-top: 10px;
  background: rgba(255, 255, 255, 0.9) !important;
  border: none !important;
  color: #333 !important;
  font-weight: 600;
  transition: all 0.3s ease;
}

.login-button:hover {
  background: #fff !important;
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-card {
    width: 90%;
    padding: 30px;
  }
}
</style>